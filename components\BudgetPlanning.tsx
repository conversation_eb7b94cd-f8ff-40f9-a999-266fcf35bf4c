import React, { useState, useMemo } from 'react';
import { MonthlyBudget, BudgetTemplate, Category } from '../types';
import { UserDataManager } from '../utils/userDataManager';
import {
  CalendarIcon,
  PlusIcon,
  DocumentDuplicateIcon,
  TrashIcon
} from '../constants';

interface BudgetPlanningProps {
  monthlyBudgets: MonthlyBudget[];
  onMonthlyBudgetsChange: (budgets: MonthlyBudget[]) => void;
  currentCategories: Category[];
  currentIncome: number;
  formatCurrency: (amount: number) => string;
  selectedCurrency: string;
  userId: string;
}

const BudgetPlanning: React.FC<BudgetPlanningProps> = ({
  monthlyBudgets,
  onMonthlyBudgetsChange,
  currentCategories,
  currentIncome,
  formatCurrency,
  selectedCurrency,
  userId
}) => {
  const [selectedMonth, setSelectedMonth] = useState<string>(() => {
    const now = new Date();
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
    return UserDataManager.formatMonthKey(nextMonth.getFullYear(), nextMonth.getMonth() + 1);
  });

  // Generate available months (current month + next 12 months)
  const availableMonths = useMemo(() => {
    const months = [];
    const now = new Date();
    
    for (let i = 0; i < 13; i++) {
      const date = new Date(now.getFullYear(), now.getMonth() + i, 1);
      const monthKey = UserDataManager.formatMonthKey(date.getFullYear(), date.getMonth() + 1);
      const monthName = UserDataManager.getMonthName(date.getFullYear(), date.getMonth() + 1);
      months.push({ key: monthKey, name: monthName });
    }
    
    return months;
  }, []);

  // Get current budget for selected month
  const currentBudget = useMemo(() => {
    return monthlyBudgets.find(budget => budget.month === selectedMonth);
  }, [monthlyBudgets, selectedMonth]);

  const createBudgetFromCurrent = () => {
    const { year, month } = UserDataManager.parseMonthKey(selectedMonth);
    const monthName = UserDataManager.getMonthName(year, month);
    
    const newBudget: MonthlyBudget = {
      id: UserDataManager.generateMonthlyBudgetId(),
      month: selectedMonth,
      year,
      monthName,
      totalIncome: currentIncome,
      categories: currentCategories.map(cat => ({
        ...cat,
        spentAmount: 0,
        subcategories: cat.subcategories.map(sub => ({
          ...sub,
          spentAmount: 0,
          isComplete: false
        }))
      })),
      transactions: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const updatedBudgets = [...monthlyBudgets.filter(b => b.month !== selectedMonth), newBudget];
    onMonthlyBudgetsChange(updatedBudgets);
  };

  const deleteBudget = (budgetId: string) => {
    const updatedBudgets = monthlyBudgets.filter(b => b.id !== budgetId);
    onMonthlyBudgetsChange(updatedBudgets);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-semibold text-sky-400">Budget Planning</h2>
          <p className="text-slate-400 mt-1">
            Plan your budgets for future months and create reusable templates
          </p>
        </div>
      </div>

      {/* Month Selection */}
      <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
        <h3 className="text-xl font-semibold text-sky-400 mb-4">Select Month to Plan</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
          {availableMonths.map(month => (
            <button
              key={month.key}
              onClick={() => setSelectedMonth(month.key)}
              className={`p-4 rounded-lg border transition-all duration-200 ${
                selectedMonth === month.key
                  ? 'bg-sky-600 border-sky-500 text-white'
                  : 'bg-slate-700 border-slate-600 text-slate-300 hover:bg-slate-600'
              }`}
            >
              <CalendarIcon className="w-5 h-5 mx-auto mb-2" />
              <div className="text-sm font-medium">{month.name}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Current Budget Status */}
      <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
        <h3 className="text-xl font-semibold text-sky-400 mb-4">
          Budget for {availableMonths.find(m => m.key === selectedMonth)?.name}
        </h3>
        
        {currentBudget ? (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-slate-300">Budget exists for this month</span>
              <button
                onClick={() => deleteBudget(currentBudget.id)}
                className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg transition-colors"
              >
                <TrashIcon className="w-4 h-4" />
                <span>Delete Budget</span>
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-slate-700 p-4 rounded-lg">
                <p className="text-slate-400 text-sm">Total Income</p>
                <p className="text-xl font-bold text-emerald-400">{formatCurrency(currentBudget.totalIncome)}</p>
              </div>
              <div className="bg-slate-700 p-4 rounded-lg">
                <p className="text-slate-400 text-sm">Categories</p>
                <p className="text-xl font-bold text-sky-400">{currentBudget.categories.length}</p>
              </div>
              <div className="bg-slate-700 p-4 rounded-lg">
                <p className="text-slate-400 text-sm">Total Allocated</p>
                <p className="text-xl font-bold text-purple-400">
                  {formatCurrency(currentBudget.categories.reduce((sum, cat) => sum + cat.allocatedAmount, 0))}
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <CalendarIcon className="w-16 h-16 text-slate-600 mx-auto mb-4" />
            <p className="text-slate-400 mb-6">No budget created for this month yet</p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={createBudgetFromCurrent}
                className="flex items-center space-x-2 bg-sky-600 hover:bg-sky-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <DocumentDuplicateIcon className="w-4 h-4" />
                <span>Copy Current Budget</span>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Existing Monthly Budgets */}
      {monthlyBudgets.length > 0 && (
        <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
          <h3 className="text-xl font-semibold text-sky-400 mb-4">Existing Monthly Budgets</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {monthlyBudgets
              .sort((a, b) => a.month.localeCompare(b.month))
              .map(budget => (
                <div key={budget.id} className="bg-slate-700 p-4 rounded-lg border border-slate-600">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-semibold text-slate-200">{budget.monthName}</h4>
                      <p className="text-sm text-slate-400">Created: {new Date(budget.createdAt).toLocaleDateString()}</p>
                    </div>
                    <button
                      onClick={() => deleteBudget(budget.id)}
                      className="text-red-400 hover:text-red-300 p-1"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-slate-400">Income:</span>
                      <span className="text-emerald-400">{formatCurrency(budget.totalIncome)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-slate-400">Categories:</span>
                      <span className="text-sky-400">{budget.categories.length}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-slate-400">Transactions:</span>
                      <span className="text-purple-400">{budget.transactions.length}</span>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default BudgetPlanning;
